<!-- content -->
<div id="content" class="app-content" role="main">

	<nav class="navbar navbar-controls bg-white">
		<div class="container-fluid">
			<!-- Brand and toggle get grouped for better mobile display -->
			<div class="navbar-header full">
				<ul class="nav navbar-nav nav-pills nav-list-button navbar-left">
					<li><button id="go-to-list" type="button" class="btn btn-default no-border navbar-btn navbar-left"><i class="fa fa-chevron-left"></i> Back</button></li>
					<li>
						<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">History <i class="caret"></i></button>
						<div class="dropdown-menu w-xxl">
							<div class="panel bg-white bg-inherit">
								<div class="panel-heading b-light bg-light">
								<strong>History</strong>
								</div>
								<div class="list-group list-group-alt" id="view-history">
									<?php
										$this->renderPartial('_view_cra_history',array('histories'=>$histories));
									?>
								</div>
							</div>
						</div>
					</li>

					<!-- SCI Groups Buttons -->
					<?php if(in_array(Yii::app()->user->groupName, array("SCI Manager", "SCI Staff"))): ?>
						<?php if($item->status != "COMPLETED"): ?>
						<li>
							<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">Reject <i class="caret"></i></button>
							<div class="dropdown-menu keep-dropdown w-xxl">
								<div class="panel bg-white bg-inherit">
									<div class="panel-heading b-light bg-light">
										<strong>Reason to reject</strong>
									</div>
									<div class="panel-body">
										<textarea class="form-control" name="reason-to-reject" id="reason-to-reject" rows="3"></textarea>
									</div>
									<div class="panel-footer text-right">
										<button class="btn btn-default cancel">Cancel</button>
										<button class="btn btn-primary" id="reject">Reject</button>
									</div>
								</div>
							</div>
						</li>
						<?php endif; ?>

						<?php if($item->status != "COMPLETED" && $item->status != "CANCEL" && in_array(Yii::app()->user->groupName, array("SCI Manager"))): ?>
						<li>
							<div class="btn-group">
								<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">
									<?php if(isset($item->assigneeId) && !empty($item->assigneeId)): ?>
										Re-assign to STRAD Staff
									<?php else: ?>
										Assign to STRAD Staff
									<?php endif; ?>
									<i class="caret"></i>
								</button>
								<ul class="dropdown-menu w-xxl">
									<?php if(isset($sci_staffs) && is_array($sci_staffs) && count($sci_staffs) > 0): ?>
										<?php foreach($sci_staffs as $sci_staff):?>
										<li class="assign-single" id="<?php echo $sci_staff->id;?>"><a href="#"><?php echo $sci_staff->fullName;?></a></li>
										<?php endforeach;?>
									<?php else: ?>
										<li><a href="#">No SCI staff available</a></li>
									<?php endif; ?>
								</ul>
							</div>
						</li>
						<?php endif; ?>

						<?php if($item->status == "WAIT_FOR_STRAD_RISK_ASSESSMENT"): ?>
						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="save-risk-assessment">Save Risk Assessment</button></li>
						<?php endif; ?>

						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="save">Save</button></li>

						<?php if($item->status == "WAIT_FOR_APPROVAL"): ?>
						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="approve">Approve</button></li>
						<?php endif; ?>
					<?php endif; ?>
				</ul>
			</div>
		</div><!-- /.container-fluid -->
	</nav>

	<div class="p-md">
		<form id="cra-form" role="form" class="form-horizontal ng-pristine ng-valid" method="post" enctype="multipart/form-data" action="<?php echo Yii::app()->createUrl("cra/write");?>">
		<input type="hidden" name="typeAction" id="typeAction">
		<input type="hidden" name="id" id="id" value="<?php echo $item->id;?>">

			<?php if($item->status == 'WAIT_FOR_MKT_SUBMISSION'): ?>
				<!-- For WAIT_FOR_MKT_SUBMISSION status, show only the form like create page -->
				<?php
				$this->renderPartial('_form',array('item'=>$item,'requests'=>$requests,'typeForm'=>'process'));
				?>
			<?php else: ?>
				<!-- For other statuses, show full process page with processing info and claims -->
				<h2>PROCESSING INFORMATION<?php if(isset($item->deleted)) echo ' (CRA IS DELETED AT '.date("H:i d-m-Y",$item->deleted/1000).')'?></h2>
				<?php
				$this->renderPartial('_process_form',array('item'=>$item,'requests'=>$requests));
				?>

				<h2>CLAIM RISK ASSESSMENT</h2>
				<?php
				$this->renderPartial('_claims_section',array(
					'item'=>$item,
					'claims'=>$claims,
					'robustnessOptions'=>$robustnessOptions,
					'finePenaltyOptions'=>$finePenaltyOptions,
					'showClaimForm'=>true
				));
				?>

				<h2>CRA DETAILS</h2>
				<?php
				$this->renderPartial('_form',array('item'=>$item,'requests'=>$requests,'typeForm'=>'process'));
				?>
			<?php endif; ?>

		</form>
	</div>
</div>
<!-- / content -->

<!-- Note: Robustness and Fine/Penalty modals are now dynamically created by cra-claims.js -->

<script>
// Set authentication token for API calls
window.accessToken = '<?php echo isset(Yii::app()->session['token']) ? Yii::app()->session['token'] : ''; ?>';

// Set global variables for JavaScript
window.baseUrl = '<?php echo Yii::app()->createUrl(''); ?>';
window.userGroupName = '<?php echo Yii::app()->user->groupName; ?>';
window.currentPage = 'process';
window.userId = <?php echo Yii::app()->user->id; ?>;

// Set CRA status for conditional button logic
window.craStatus = '<?php echo isset($item->status) ? $item->status : ''; ?>';

// Initialize claims data if available
<?php if(isset($claims) && !empty($claims)): ?>
window.claimsData = <?php echo json_encode($claims); ?>;
<?php else: ?>
window.claimsData = [];
<?php endif; ?>

// Initialize reference data if available
<?php if(isset($robustnessOptions) && !empty($robustnessOptions)): ?>
window.robustnessOptions = <?php echo json_encode($robustnessOptions); ?>;
<?php else: ?>
window.robustnessOptions = [];
<?php endif; ?>

<?php if(isset($finePenaltyOptions) && !empty($finePenaltyOptions)): ?>
window.finePenaltyOptions = <?php echo json_encode($finePenaltyOptions); ?>;
<?php else: ?>
window.finePenaltyOptions = [];
<?php endif; ?>

// Wait for jQuery to be loaded before executing
function initializeCraProcessPage() {
	// Check if jQuery is available
	if (typeof $ === 'undefined') {
		// jQuery not loaded yet, wait and try again
		setTimeout(initializeCraProcessPage, 100);
		return;
	}

	$(document).ready(function() {
	// Assign single CRA
	$(document).on('click', '.assign-single', function(e){
		e.preventDefault();
		var assigneeId = $(this).attr('id');
		var craId = $('#id').val();

		if (!assigneeId) {
			jubiqAlert('warning', 'Invalid staff selected.');
			return false;
		}

		$.ajax({
			url: baseUrl + '/cra/assign',
			type: "POST",
			dataType: "json",
			data: {
				selected_items: craId,
				assigneeId: assigneeId,
				single_assign: 'true'
			},
			success: function(result) {
				if (result.success) {
					jubiqAlert('success', 'CRA request assigned successfully!');
					// Reload the page to reflect the change
					setTimeout(function() {
						location.reload();
					}, 1500);
				} else {
					jubiqAlert('danger', result.error || 'Failed to assign CRA request');
				}
			},
			error: function(result) {
				jubiqAlert('danger', result.responseText || 'An error occurred while assigning the request.');
			}
		});
		return false;
	});

	// Navigation
	$('#go-to-list').click(function() {
		window.location.href = baseUrl + '/cra/index';
	});

	// Initialize download functionality for files
	$('.download-file').on('click', function(event) {
		var fileId = $(this).closest('tr').attr('id');
		var url = baseUrl + '/file/download/' + fileId;
		window.open(url, '_blank');
		return false;
	});

	// Initialize download functionality for chat attachments
	$('a.a-download-file').on('click', function(event) {
		var fileId = $(this).attr('id');
		var url = baseUrl + '/file/download/' + fileId;
		window.open(url, '_blank');
		return false;
	});

	// Remove file functionality
	$('.remove-file').on('click', function(event) {
		$(this).closest('tr').remove();
		return false;
	});

		// Note: All claim management functionality is handled by cra-claims.js
		// which provides consistent behavior across both update and process pages
	});
}

// Start initialization
initializeCraProcessPage();
</script>
