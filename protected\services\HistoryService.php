<?php

/**
 * JService represents an script for connecting to RESTFUL API
 */
class HistoryService extends JService
{
	public static $apiModule = "histories";

	public static $list_fields = array(
		'presentationType' => 'Presentation Type',
		'productTypeDesc' => 'Product Type',
		'awFiles' => 'AW Files',
		'launchFormFiles' => 'Launch Form',
		'cfsFiles' => 'CFS Files',
		'inciFiles' => 'INCI Files',
		'davFiles' => 'DAV/IMDA Files',
		'notiType' => 'Notification Type',
		'reasonUrgent' => 'Urgent Reason',
		'contentReasonOther' => 'Other Reason',
		'brandName' => 'Brand',
		'productRangeName' => 'Range',
		'productName' => 'English Product Name',
		'productNameVn' => 'Vietnamese Product Name',
		'launchTime' => 'Launch time',
		'shipmentRequestTime' => 'Shipment request time',
		'validatedTime' => 'SCI Validated Time',
		'cfsRequestingDate' => 'Date of requesting CFS',
		'cfsReceivingDate' => 'Date of receiving CFS',
		'inciRequestingDate' => 'Date of requesting INCI',
		'inciReceivingDate' => 'Date of receiving INCI',
		'davRequestingDate' => 'Date of submitting to DAV/IMDA',
		'davOfficialRequestingDate' => 'DAV/IMDA\'s Official Date of Receiving Submission After Payment',
		'davNotificationNumber' => 'DAV/IMDA Notification Number',
		'davReceivingDate' => 'Date of receiving from DAV/IMDA',
		'davExpiringDate' => 'Date of expiring DAV/IMDA',
		'presentationDetails' => 'Product Presentation'

	);

	/**
	 * Compare values
	 */
	public static function compare($history)
	{
		$result = array();
		$oldValue = json_decode($history->oldValue, true);
		$newValue = json_decode($history->newValue, true);


		if (!isset($oldValue['presentationType']) || ($oldValue['presentationType'] != $newValue['presentationType'])) {
			$result['presentationType'] = array(
				'old' => (isset($oldValue['presentationType']) && isset(NotificationService::$list_presentation_types[$oldValue['presentationType']])) ? NotificationService::$list_presentation_types[$oldValue['presentationType']] : "",
				'new' => NotificationService::$list_presentation_types[$newValue['presentationType']]
			);
		}

		if (!isset($oldValue['productTypeDesc']) || ($oldValue['productTypeDesc'] != $newValue['productTypeDesc'])) {
			$result['productTypeDesc'] = array(
				'old' => nl2br(isset($oldValue['productTypeDesc']) ? $oldValue['productTypeDesc'] : ""),
				'new' => nl2br(isset($newValue['productTypeDesc']) ? $newValue['productTypeDesc'] : "")
			);
		}

		$oldFiles = isset($oldValue['awFiles']) ? $oldValue['awFiles'] : [];
		$newFiles = isset($newValue['awFiles']) ? $newValue['awFiles'] : [];

		$deleted = array_diff_key($oldFiles, $newFiles);
		$added   = array_diff_key($newFiles, $oldFiles);

		if (count($deleted) > 0 || count($added) > 0) {
			$result['awFiles'] = array(
				'old' => "",
				'new' => "Xóa " . count($deleted) . " file & Thêm " . count($added) . " file"
			);
		}

		$oldFiles = isset($oldValue['launchFormFiles']) ? $oldValue['launchFormFiles'] : [];
		$newFiles = isset($newValue['launchFormFiles']) ? $newValue['launchFormFiles'] : [];

		$deleted = array_diff_key($oldFiles, $newFiles);
		$added   = array_diff_key($newFiles, $oldFiles);

		if (count($deleted) > 0 || count($added) > 0) {
			$result['launchFormFiles'] = array(
				'old' => "",
				'new' => "Xóa " . count($deleted) . " file & Thêm " . count($added) . " file"
			);
		}

		$oldFiles = isset($oldValue['cfsFiles']) ? $oldValue['cfsFiles'] : [];
		$newFiles = isset($newValue['cfsFiles']) ? $newValue['cfsFiles'] : [];

		$deleted = array_diff_key($oldFiles, $newFiles);
		$added   = array_diff_key($newFiles, $oldFiles);

		if (count($deleted) > 0 || count($added) > 0) {
			$result['cfsFiles'] = array(
				'old' => "",
				'new' => "Xóa " . count($deleted) . " file & Thêm " . count($added) . " file"
			);
		}

		$oldFiles = isset($oldValue['inciFiles']) ? $oldValue['inciFiles'] : [];
		$newFiles = isset($newValue['inciFiles']) ? $newValue['inciFiles'] : [];

		$deleted = array_diff_key($oldFiles, $newFiles);
		$added   = array_diff_key($newFiles, $oldFiles);

		if (count($deleted) > 0 || count($added) > 0) {
			$result['inciFiles'] = array(
				'old' => "",
				'new' => "Xóa " . count($deleted) . " file & Thêm " . count($added) . " file"
			);
		}

		$oldFiles = isset($oldValue['davFiles']) ? $oldValue['davFiles'] : [];
		$newFiles = isset($newValue['davFiles']) ? $newValue['davFiles'] : [];

		$deleted = array_diff_key($oldFiles, $newFiles);
		$added   = array_diff_key($newFiles, $oldFiles);

		if (count($deleted) > 0 || count($added) > 0) {
			$result['davFiles'] = array(
				'old' => "",
				'new' => "Xóa " . count($deleted) . " file & Thêm " . count($added) . " file"
			);
		}

		$list_datas = array(
			'notiType',
			'reasonUrgent',
			'contentReasonOther',
			'brandName',
			'productRangeName',
			'productName',
			'productNameVn',
			'launchTime',
			'shipmentRequestTime',
			'validatedTime',
			'cfsRequestingDate',
			'cfsReceivingDate',
			'inciRequestingDate',
			'inciReceivingDate',
			'davRequestingDate',
			'davOfficialRequestingDate',
			'davNotificationNumber',
			'davReceivingDate',
			'davExpiringDate'
		);

		foreach ($list_datas as $data) {
			if ((!isset($oldValue[$data]) && isset($newValue[$data])) || (!isset($newValue[$data]) && isset($oldValue[$data])) || (isset($newValue[$data]) && isset($oldValue[$data]) && $oldValue[$data] != $newValue[$data])) {
				$result[$data] = array(
					'old' => (isset($oldValue[$data]) ? $oldValue[$data] : ""),
					'new' => (isset($newValue[$data]) ? $newValue[$data] : "")
				);
			}
		}

		$comparePresentationDetails = self::comparePresentationDetails($oldValue['presentationDetails'], $newValue['presentationDetails']);

		if ($comparePresentationDetails['deletedCount'] > 0 || $comparePresentationDetails['addedCount'] > 0) {
			$result['presentationDetails'] = array(
				'old' => "",
				'new' => "Xóa " . $comparePresentationDetails['deletedCount'] . " SKU & Thêm " . $comparePresentationDetails['addedCount'] . " SKU"
			);
		}

		foreach ($comparePresentationDetails['changedDetails'] as $formulaNumber => $changes) {
			$changeDetails = [];
			foreach ($changes as $field => $change) {
				$result["SKU - $formulaNumber - $field"] = [
					'old' => $change['old'],
					'new' => $change['new']
				];
			}
		}

		return $result;
	}

	function comparePresentationDetails($oldList, $newList)
	{
		// Bước 1: Ánh xạ theo id
		$oldMap = [];
		foreach ($oldList as $item) {
			$oldMap[$item['id']] = $item;
		}

		$newMap = [];
		foreach ($newList as $item) {
			$newMap[$item['id']] = $item;
		}

		// Bước 2: Tìm object bị xóa và thêm mới
		$deletedIds = array_diff_key($oldMap, $newMap);
		$addedIds   = array_diff_key($newMap, $oldMap);

		// Bước 3: So sánh object chung ID theo 3 trường cụ thể
		$fieldsToCheck = ['productCode', 'barCode', 'formulaNumber', 'filCode', 'compositeNumber', 'netWeight', 'volume', 'shadeName', 'shadeCode', 'manufacturerName', 'assemblerName', 'exporterName', 'individualIntendedUse', 'individualWarning',];
		$changed = [];

		foreach (array_intersect_key($oldMap, $newMap) as $id => $oldItem) {
			$newItem = $newMap[$id];
			$diff = [];

			foreach ($fieldsToCheck as $field) {
				$oldVal = isset($oldItem[$field]) ? $oldItem[$field] : null;
				$newVal = isset($newItem[$field]) ? $newItem[$field] : null;

				if ($oldVal !== $newVal) {
					$diff[$field] = ['old' => $oldVal, 'new' => $newVal];
				}
			}

			if (!empty($diff)) {
				$changed[$newItem['formulaNumber']] = $diff;
			}
		}

		return [
			'deletedCount' => count($deletedIds),
			'addedCount' => count($addedIds),
			'changedCount' => count($changed),
			'deletedIds' => array_keys($deletedIds),
			'addedIds' => array_keys($addedIds),
			'changedDetails' => $changed
		];
	}

	/**
	 * View an alert
	 */
	public static function view($history)
	{
		$action = $history->action;
		if ($action == 'reject-product-name')
			$action = 'reject (sc)';
		elseif ($action == 'validate-product-name')
			$action = 'validate (sc)';
		$result = "<b>" . $history->actorFullName . "</b> " . $action;
		return $result;
	}

	/**
	 * Get all histories of notification
	 */
	public static function getHistoriesOfNotification($notificationId)
	{
		if (isset(Yii::app()->session['token'])) {
			$data = array(
				"notificationId" => $notificationId,
			);

			$url = 	self::$baseUrl . "/histories/query?" . http_build_query($data);
			$curl = curl_init($url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
				"Accept: application/json",
				"Authorization: Bearer " . Yii::app()->session['token']
			));
			// Make the REST call, returning the result
			$response = curl_exec($curl);

			if ($response === FALSE) {
				throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if ($http_status == 200) {
				return $result;
			} elseif ($http_status == 401 && !isset($result->errorCode)) {
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			} else {
				throw new CHttpException($http_status, $result->errorMessage . '.');
			}
		} else {
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	/**
	 * Get all histories of label
	 */
	public static function getHistoriesOfLabel($labelId)
	{
		if (isset(Yii::app()->session['token'])) {
			$data = array(
				"labelId" => $labelId,
			);

			$url = 	self::$baseUrl . "/histories/query-label?" . http_build_query($data);
			$curl = curl_init($url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
				"Accept: application/json",
				"Authorization: Bearer " . Yii::app()->session['token']
			));
			// Make the REST call, returning the result
			$response = curl_exec($curl);

			if ($response === FALSE) {
				throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if ($http_status == 200) {
				return $result;
			} elseif ($http_status == 401 && !isset($result->errorCode)) {
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			} else {
				throw new CHttpException($http_status, $result->errorMessage . '.');
			}
		} else {
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	/**
	 * Get all histories of advertising
	 */
	public static function getHistoriesOfAdvertising($advertisingId)
	{
		if (isset(Yii::app()->session['token'])) {
			$data = array(
				"advertisingId" => $advertisingId,
			);

			$url = 	self::$baseUrl . "/histories/query-advertising?" . http_build_query($data);
			$curl = curl_init($url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
				"Accept: application/json",
				"Authorization: Bearer " . Yii::app()->session['token']
			));
			// Make the REST call, returning the result
			$response = curl_exec($curl);

			if ($response === FALSE) {
				throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if ($http_status == 200) {
				return $result;
			} elseif ($http_status == 401 && !isset($result->errorCode)) {
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			} else {
				throw new CHttpException($http_status, $result->errorMessage . '.');
			}
		} else {
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	/**
	 * Get all histories of CRA
	 */
	public static function getHistoriesOfCra($craId)
	{
		if (isset(Yii::app()->session['token'])) {
			$data = array(
				"craId" => $craId,
			);

			$url = 	self::$baseUrl . "/histories/query-cra?" . http_build_query($data);
			$curl = curl_init($url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
				"Accept: application/json",
				"Authorization: Bearer " . Yii::app()->session['token']
			));
			// Make the REST call, returning the result
			$response = curl_exec($curl);

			if ($response === FALSE) {
				throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if ($http_status == 200) {
				return $result;
			} elseif ($http_status == 401 && !isset($result->errorCode)) {
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			} else {
				throw new CHttpException($http_status, $result->errorMessage . '.');
			}
		} else {
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	/**
	 * Get all histories of CRA including claims history
	 */
	public static function getHistoriesOfCraWithClaims($craId)
	{
		if (isset(Yii::app()->session['token'])) {
			$data = array(
				"craId" => $craId,
			);

			$url = 	self::$baseUrl . "/histories/query-cra-with-claims?" . http_build_query($data);
			$curl = curl_init($url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
				"Accept: application/json",
				"Authorization: Bearer " . Yii::app()->session['token']
			));
			// Make the REST call, returning the result
			$response = curl_exec($curl);

			if ($response === FALSE) {
				throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if ($http_status == 200) {
				return $result;
			} elseif ($http_status == 401 && !isset($result->errorCode)) {
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			} else {
				throw new CHttpException($http_status, $result->errorMessage . '.');
			}
		} else {
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}

	/**
	 * Compare values
	 */
	public static function compareLabelHistory($history)
	{
		$result = array();
		$oldValue = json_decode($history->oldValue, true);
		$newValue = json_decode($history->newValue, true);
		$list_datas = array(
			'productName',
			'productNameVn',
			'userManual',
			'importer',
			'owner',
			'distributor',
			'lotNumber',
			'manufacturingDate',
			'expirationDate',
			'hotline',
			'davNotificationNumber',
			'manufacturer',
			'preservation',
			'netVolume',
			'uses',
			'alarm',
			'ingredients',
			'baseIngredients',
			'colorIngredients',
			'barCode',
			'sapCode',
			'other'
		);
		foreach ($list_datas as $data) {
			if ((!isset($oldValue[$data]) && isset($newValue[$data])) || (!isset($newValue[$data]) && isset($oldValue[$data])) || (isset($newValue[$data]) && isset($oldValue[$data]) && $oldValue[$data] != $newValue[$data])) {
				$result[$data] = array(
					'old' => (isset($oldValue[$data]) ? $oldValue[$data] : ""),
					'new' => (isset($newValue[$data]) ? $newValue[$data] : "")
				);
			}
		}

		return $result;
	}

	/**
	 * Compare values
	 */
	public static function compareAdvertisingHistory($history)
	{
		$result = array();
		$oldValue = json_decode($history->oldValue, true);
		$newValue = json_decode($history->newValue, true);
		$list_datas = array(
			'advertisingMediaSelection',
		);
		foreach ($list_datas as $data) {
			if ((!isset($oldValue[$data]) && isset($newValue[$data])) || (!isset($newValue[$data]) && isset($oldValue[$data])) || (isset($newValue[$data]) && isset($oldValue[$data]) && $oldValue[$data] != $newValue[$data])) {
				$result[$data] = array(
					'old' => (isset($oldValue[$data]) ? $oldValue[$data] : ""),
					'new' => (isset($newValue[$data]) ? $newValue[$data] : "")
				);
			}
		}

		return $result;
	}

	/**
	 * Compare values for CRA history
	 */
	public static function compareCraHistory($history)
	{
		$result = array();
		$oldValue = json_decode($history->oldValue, true);
		$newValue = json_decode($history->newValue, true);

		// List of CRA fields that should be compared in history
		$list_datas = array(
			'requestIds',
			'timeline',
			'advertisementType',
			'contentFiles',
			'referencesFiles',
			'proofDocuments',
			'stradRiskAssessmentFiles',
			'dateOfSubmitting',
			'dateOfRequestingProofDocument',
			'dateOfReceivingProofDocument',
			'dateOfStradRiskAssessment',
			'dateOfCompleted',
			'assigneeEmail',
			'followerEmail'
		);

		// Handle exposition (combination of expositionLevel and expositionDetail)
		$oldExpositionLevel = isset($oldValue['expositionLevel']) ? $oldValue['expositionLevel'] : '';
		$oldExpositionDetail = isset($oldValue['expositionDetail']) ? $oldValue['expositionDetail'] : '';
		$newExpositionLevel = isset($newValue['expositionLevel']) ? $newValue['expositionLevel'] : '';
		$newExpositionDetail = isset($newValue['expositionDetail']) ? $newValue['expositionDetail'] : '';

		// Check if exposition changed (either level or detail)
		if ($oldExpositionLevel != $newExpositionLevel || $oldExpositionDetail != $newExpositionDetail) {
			// Format old exposition
			$oldExposition = '';
			if (!empty($oldExpositionLevel) && !empty($oldExpositionDetail)) {
				$oldExposition = $oldExpositionLevel . ' - ' . $oldExpositionDetail;
			} elseif (!empty($oldExpositionLevel)) {
				$oldExposition = $oldExpositionLevel;
			} elseif (!empty($oldExpositionDetail)) {
				$oldExposition = $oldExpositionDetail;
			}

			// Format new exposition
			$newExposition = '';
			if (!empty($newExpositionLevel) && !empty($newExpositionDetail)) {
				$newExposition = $newExpositionLevel . ' - ' . $newExpositionDetail;
			} elseif (!empty($newExpositionLevel)) {
				$newExposition = $newExpositionLevel;
			} elseif (!empty($newExpositionDetail)) {
				$newExposition = $newExpositionDetail;
			}

			$result['exposition'] = array(
				'old' => $oldExposition,
				'new' => $newExposition
			);
		}

		foreach ($list_datas as $data) {
			if ((!isset($oldValue[$data]) && isset($newValue[$data])) ||
				(!isset($newValue[$data]) && isset($oldValue[$data])) ||
				(isset($newValue[$data]) && isset($oldValue[$data]) && $oldValue[$data] != $newValue[$data])) {

				// Handle different data types appropriately
				$oldDisplay = self::formatHistoryValue($data, isset($oldValue[$data]) ? $oldValue[$data] : null);
				$newDisplay = self::formatHistoryValue($data, isset($newValue[$data]) ? $newValue[$data] : null);

				$result[$data] = array(
					'old' => $oldDisplay,
					'new' => $newDisplay
				);
			}
		}

		return $result;
	}

	/**
	 * Format history value for display based on field type
	 */
	private static function formatHistoryValue($fieldName, $value)
	{
		if ($value === null || $value === '') {
			return '';
		}

		// Handle array fields (files and requestIds)
		$arrayFields = array('contentFiles', 'referencesFiles', 'proofDocuments', 'stradRiskAssessmentFiles', 'requestIds');
		if (in_array($fieldName, $arrayFields)) {
			if (is_array($value)) {
				if ($fieldName === 'requestIds') {
					return implode(', ', $value);
				} else {
					// For file fields, show count
					$count = count($value);
					return $count . ($count == 1 ? ' file' : ' files');
				}
			} else {
				return $value; // Fallback for non-array values
			}
		}

		// Handle date fields
		$dateFields = array('timeline', 'dateOfSubmitting', 'dateOfRequestingProofDocument',
						   'dateOfReceivingProofDocument', 'dateOfStradRiskAssessment', 'dateOfCompleted');
		if (in_array($fieldName, $dateFields)) {
			if (is_numeric($value)) {
				return date('Y-m-d H:i:s', $value / 1000); // Convert from milliseconds
			}
		}

		// For other fields, return as string
		return (string) $value;
	}

	/**
	 * Compare values for CRA Claims history
	 */
	public static function compareClaimsHistory($history)
	{
		$result = array();
		$oldValue = json_decode($history->oldValue, true);
		$newValue = json_decode($history->newValue, true);

		// List of CRA Claims fields that should be compared in history
		$list_datas = array(
			'craId',
			'claimType',
			'claims',
			'framedRisk',
			'criticalRisk',
			'fineAndPenaltyId',
			'mktAcceptedStatus',
			'mktAcceptedDate',
			'mktAcceptedBy',
			'approvalStatus',
			'approvalDate',
			'approverId',
			'approverType',
			'detail',
			'robustnessId'
		);

		foreach ($list_datas as $data) {
			if ((!isset($oldValue[$data]) && isset($newValue[$data])) ||
				(!isset($newValue[$data]) && isset($oldValue[$data])) ||
				(isset($newValue[$data]) && isset($oldValue[$data]) && $oldValue[$data] != $newValue[$data])) {

				// Handle different data types appropriately
				$oldDisplay = self::formatClaimsHistoryValue($data, isset($oldValue[$data]) ? $oldValue[$data] : null);
				$newDisplay = self::formatClaimsHistoryValue($data, isset($newValue[$data]) ? $newValue[$data] : null);

				$result[$data] = array(
					'old' => $oldDisplay,
					'new' => $newDisplay
				);
			}
		}

		return $result;
	}

	/**
	 * Format claims history value for display based on field type
	 */
	private static function formatClaimsHistoryValue($fieldName, $value)
	{
		if ($value === null || $value === '') {
			return '';
		}

		// Handle date fields
		$dateFields = array('mktAcceptedDate', 'approvalDate');
		if (in_array($fieldName, $dateFields)) {
			if (is_numeric($value)) {
				return date('Y-m-d H:i:s', $value / 1000); // Convert from milliseconds
			} elseif (is_string($value)) {
				// Handle ISO date strings
				$timestamp = strtotime($value);
				if ($timestamp !== false) {
					return date('Y-m-d H:i:s', $timestamp);
				}
			}
		}

		// Handle ID fields - try to resolve to names
		if ($fieldName === 'craId') {
			return 'CRA Request #' . $value;
		} elseif ($fieldName === 'fineAndPenaltyId') {
			// Could be enhanced to lookup actual fine/penalty name
			return 'Fine/Penalty ID: ' . $value;
		} elseif ($fieldName === 'robustnessId') {
			// Could be enhanced to lookup actual robustness name
			return 'Robustness ID: ' . $value;
		} elseif ($fieldName === 'mktAcceptedBy' || $fieldName === 'approverId') {
			// Could be enhanced to lookup actual user name
			return 'User ID: ' . $value;
		}

		// Handle status fields with user-friendly display
		if ($fieldName === 'mktAcceptedStatus') {
			switch (strtoupper($value)) {
				case 'ACCEPTED': return 'Accepted';
				case 'NOT_ACCEPTED': return 'Not Accepted';
				default: return $value;
			}
		} elseif ($fieldName === 'approvalStatus') {
			switch (strtoupper($value)) {
				case 'APPROVED': return 'Approved';
				case 'NOT_APPROVED': return 'Not Approved';
				case 'PENDING_GM': return 'Pending GM';
				case 'PENDING_CM': return 'Pending CM';
				default: return $value;
			}
		} elseif ($fieldName === 'claimType') {
			switch (strtolower($value)) {
				case 'text': return 'Text Claim';
				case 'image': return 'Image Claim';
				default: return ucfirst($value);
			}
		}

		// For other fields, return as string
		return (string) $value;
	}
}
