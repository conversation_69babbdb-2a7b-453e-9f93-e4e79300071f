<div class="row">
	<!-- Left Panel - Status Indicators -->
	<div class="col-md-6">
		<div class="panel panel-default">
			<div class="panel-body">
				<div role="form" class="form-horizontal ng-pristine ng-valid">
					<!-- CRA Number -->
					<div class="form-group">
						<label class="col-sm-4 control-label">CRA Number</label>
						<div class="col-sm-8">
							<input readonly class="form-control" value="<?php echo $item->id; ?>">
						</div>
					</div>

					<!-- Status -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Status</label>
						<div class="col-sm-8">
							<input readonly class="form-control" value="<?php echo $item->status; ?>">
						</div>
					</div>

					<!-- Status Checkboxes (Auto-checked based on data) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Requesting Proof Document</label>
						<div class="col-sm-8">
							<div class="checkbox">
								<label class="ui-checks">
									<input type="checkbox" disabled <?php echo !empty($item->dateOfRequestingProofDocument) ? 'checked' : ''; ?>>
									<i></i>
								</label>
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-4 control-label">Proof Document Available</label>
						<div class="col-sm-8">
							<div class="checkbox">
								<label class="ui-checks">
									<input type="checkbox" disabled <?php echo !empty($item->dateOfReceivingProofDocument) ? 'checked' : ''; ?>>
									<i></i>
								</label>
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-4 control-label">STRAD Risk Assessment</label>
						<div class="col-sm-8">
							<div class="checkbox">
								<label class="ui-checks">
									<input type="checkbox" disabled <?php echo !empty($item->dateOfStradRiskAssessment) ? 'checked' : ''; ?>>
									<i></i>
								</label>
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-4 control-label">Approval</label>
						<div class="col-sm-8">
							<div class="checkbox">
								<label class="ui-checks">
									<input type="checkbox" disabled <?php echo !empty($item->dateOfCompleted) ? 'checked' : ''; ?>>
									<i></i>
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Right Panel - Date Fields -->
	<div class="col-md-6">
		<div class="panel panel-default">
			<div class="panel-body">
				<div role="form" class="form-horizontal ng-pristine ng-valid">
					<!-- Date of Submitting (Read-only, auto-populated) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Submitting</label>
						<div class="col-sm-8">
							<input readonly class="form-control" value="<?php echo isset($item->dateOfSubmitting) ? CraService::formatDate($item->dateOfSubmitting, 'Y-m-d') : ''; ?>">
						</div>
					</div>

					<!-- Date of Requesting Proof Document (SCI-editable) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Requesting Proof Document</label>
						<div class="col-sm-8">
							<?php if(in_array(Yii::app()->user->groupName, array("SCI Manager", "SCI Staff"))): ?>
								<div class="input-group">
									<input type="text" name="dateOfRequestingProofDocument" class="form-control date-picker"
										   value="<?php echo isset($item->dateOfRequestingProofDocument) ? $item->dateOfRequestingProofDocument : ''; ?>" data-date-format="yyyy-mm-dd">
									<span class="input-group-btn">
										<button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
									</span>
								</div>
							<?php else: ?>
								<input readonly class="form-control" value="<?php echo isset($item->dateOfRequestingProofDocument) ? $item->dateOfRequestingProofDocument : ''; ?>">
							<?php endif; ?>
						</div>
					</div>

					<!-- Date of Receiving Proof Document (SCI-editable) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Receiving Proof Document</label>
						<div class="col-sm-8">
							<?php if(in_array(Yii::app()->user->groupName, array("SCI Manager", "SCI Staff"))): ?>
								<div class="input-group">
									<input type="text" name="dateOfReceivingProofDocument" class="form-control date-picker"
										   value="<?php echo isset($item->dateOfReceivingProofDocument) ? $item->dateOfReceivingProofDocument : ''; ?>" data-date-format="yyyy-mm-dd">
									<span class="input-group-btn">
										<button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
									</span>
								</div>
							<?php else: ?>
								<input readonly class="form-control" value="<?php echo isset($item->dateOfReceivingProofDocument) ? $item->dateOfReceivingProofDocument : ''; ?>">
							<?php endif; ?>
						</div>
					</div>

					<!-- Date of STRAD Risk Assessment (Read-only, auto-populated) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of STRAD Risk Assessment</label>
						<div class="col-sm-8">
							<input readonly class="form-control" value="<?php echo isset($item->dateOfStradRiskAssessment) ? $item->dateOfStradRiskAssessment : ''; ?>">
						</div>
					</div>

					<!-- Date of Approval (Table format) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Approval</label>
						<div class="col-sm-8">
							<?php if(isset($item->approvalData) && !empty($item->approvalData)): ?>
								<div class="table-responsive">
									<table class="table table-bordered table-striped">
										<thead>
											<tr>
												<th>Claim No</th>
												<th>Date of Approval</th>
											</tr>
										</thead>
										<tbody>
											<?php foreach($item->approvalData as $approval): ?>
											<tr>
												<td><?php echo $approval->claimNo; ?></td>
												<td><?php echo $approval->approvalDate; ?></td>
											</tr>
											<?php endforeach; ?>
										</tbody>
									</table>
								</div>
							<?php else: ?>
								<input readonly class="form-control" placeholder="No approvals yet">
							<?php endif; ?>
						</div>
					</div>

					<!-- Date of Completed (Read-only, auto-populated) -->
					<div class="form-group">
						<label class="col-sm-4 control-label">Date of Completed</label>
						<div class="col-sm-8">
							<input readonly class="form-control" value="<?php echo isset($item->dateOfCompleted) ? $item->dateOfCompleted : ''; ?>">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
// Initialize date pickers with jQuery availability check
function initializeDatePickers() {
	if (typeof $ === 'undefined' || typeof $.fn === 'undefined' || typeof $.fn.datepicker === 'undefined') {
		// jQuery or datepicker not loaded yet, wait and try again
		setTimeout(initializeDatePickers, 100);
		return;
	}

	$(document).ready(function() {
		$('.date-picker').datepicker({
			format: 'yyyy-mm-dd',
			autoclose: true,
			todayHighlight: true
		});
	});
}

// Start initialization
initializeDatePickers();
</script>