/**
 * CRA Claims Management JavaScript
 * Optimized for performance and maintainability
 */

// Global state management
var CraClaimsModule = (function() {
    'use strict';

    // Private variables
    var claimsData = [];
    var robustnessCache = {};
    var finePenaltyCache = {};
    var isInitialized = false;

    // Configuration
    var config = {
        selectors: {
            claimsTable: '#claims-table tbody',
            claimForm: '#claim-form',
            claimTypeRadio: 'input[name="claimType"]',
            textGroup: '#claims-text-group',
            imageGroup: '#claims-image-group',
            textInput: '#claim-text',
            imageInput: '#claim-image'
        },
        classes: {
            robustnessLink: 'robustness-link',
            finePenaltyLink: 'fine-penalty-link',
            mktAcceptBtn: 'mkt-accept-claim',
            mktRejectBtn: 'mkt-reject-claim'
        }
    };

    /**
     * Initialize claims data from server
     */
    function initializeClaimsData(serverClaimsData) {
        if (!serverClaimsData || !Array.isArray(serverClaimsData)) {
            return;
        }

        claimsData = serverClaimsData;

        // Load reference data and update table
        loadReferenceData()
            .then(enrichClaimsDataWithNames)
            .then(updateClaimsTable)
            .fail(function() {
                updateClaimsTable();
            });
    }

    /**
     * Load reference data from window objects
     */
    function loadReferenceData() {
        // Clear existing cache to prevent stale data
        robustnessCache = {};
        finePenaltyCache = {};

        // Load robustness data
        if (window.robustnessOptions && Array.isArray(window.robustnessOptions)) {
            console.log('Loading ' + window.robustnessOptions.length + ' robustness options');
            window.robustnessOptions.forEach(function(item) {
                if (item && item.id) {
                    robustnessCache[item.id] = item;
                }
            });
        } else {
            console.warn('No robustness options available in window.robustnessOptions');
        }

        // Load fine and penalty data
        if (window.finePenaltyOptions && Array.isArray(window.finePenaltyOptions)) {
            console.log('Loading ' + window.finePenaltyOptions.length + ' fine/penalty options');
            window.finePenaltyOptions.forEach(function(item) {
                if (item && item.id) {
                    finePenaltyCache[item.id] = item;
                }
            });
        } else {
            console.warn('No fine/penalty options available in window.finePenaltyOptions');
        }

        return $.Deferred().resolve().promise();
    }

    /**
     * Refresh reference data from server (cache-busting)
     */
    function refreshReferenceData() {
        console.log('Refreshing reference data from server...');

        // Add timestamp to prevent caching
        var timestamp = new Date().getTime();
        var currentUrl = window.location.href;

        // Add cache-busting parameter
        var separator = currentUrl.indexOf('?') !== -1 ? '&' : '?';
        var refreshUrl = currentUrl + separator + '_refresh=' + timestamp;

        // Reload the page to get fresh data
        window.location.href = refreshUrl;
    }

    /**
     * Enrich claims data with reference names
     */
    function enrichClaimsDataWithNames() {
        claimsData.forEach(function(claim) {
            // Set robustness name from cache if not already provided
            if (claim.robustnessId && !claim.robustnessName) {
                var robustnessItem = robustnessCache[claim.robustnessId];
                if (robustnessItem) {
                    claim.robustnessName = robustnessItem.itemName;
                } else {
                    claim.robustnessName = 'Robustness (ID: ' + claim.robustnessId + ')';
                }
            }

            // Set fine and penalty name from cache if not already provided
            if (claim.fineAndPenaltyId && !claim.fineAndPenaltyName) {
                var finePenaltyItem = finePenaltyCache[claim.fineAndPenaltyId];
                if (finePenaltyItem) {
                    claim.fineAndPenaltyName = finePenaltyItem.itemName;
                } else {
                    claim.fineAndPenaltyName = 'Fine/Penalty (ID: ' + claim.fineAndPenaltyId + ')';
                }
            }
        });

        return $.Deferred().resolve().promise();
    }

    /**
     * Update claims table (internal module function)
     */
    function updateClaimsTable() {
        var tbody = $('#claims-list');

        if (!claimsData || claimsData.length === 0) {
            tbody.html('<tr><td colspan="9" class="text-center">No claims added yet</td></tr>');
            return;
        }

        // Build all rows in memory first for better performance
        var rows = claimsData.map(function(claim) {
            return buildClaimRow(claim);
        });

        // Update DOM once
        tbody.html(rows.join(''));
    }

    /**
     * Helper functions for building table cells
     */
    function escapeHtml(text) {
        var div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function buildRobustnessCell(claim, robustnessCache) {
        if (!claim.robustnessId) return '';

        var robustnessText = claim.robustnessName;
        if (!robustnessText && robustnessCache[claim.robustnessId]) {
            robustnessText = robustnessCache[claim.robustnessId].itemName;
        }
        if (!robustnessText) {
            robustnessText = 'Robustness (ID: ' + claim.robustnessId + ')';
        }

        return '<a href="#" class="robustness-link" data-robustness-id="' + claim.robustnessId + '">' +
               escapeHtml(robustnessText) + '</a>';
    }

    function buildFinePenaltyCell(claim, finePenaltyCache) {
        if (!claim.fineAndPenaltyId) return '';

        var finePenaltyText = claim.fineAndPenaltyName;
        if (!finePenaltyText && finePenaltyCache[claim.fineAndPenaltyId]) {
            finePenaltyText = finePenaltyCache[claim.fineAndPenaltyId].itemName;
        }
        if (!finePenaltyText) {
            finePenaltyText = 'Fine/Penalty (ID: ' + claim.fineAndPenaltyId + ')';
        }

        return '<a href="#" class="fine-penalty-link" data-fine-penalty-id="' + claim.fineAndPenaltyId + '">' +
               escapeHtml(finePenaltyText) + '</a>';
    }

    function buildRiskBadge(riskValue, color) {
        if (!riskValue) return '';
        return '<span class="badge" style="background-color: ' + color + ';">' + escapeHtml(riskValue) + '</span>';
    }

    function buildMktAcceptedStatus(claim) {
        if (!claim.mktAcceptedStatus || !claim.mktAcceptedDate) return '';

        var mktDate = new Date(claim.mktAcceptedDate);
        var status = claim.mktAcceptedStatus.toLowerCase();
        var displayText = status === 'accepted' ? 'Accepted' : 'Not Accepted';
        return escapeHtml(displayText) + ' (' + mktDate.toLocaleDateString() + ')';
    }

    function buildApprovalStatus(claim) {
        if (!claim.approvalStatus) return '';

        var displayStatus = formatApprovalStatus(claim.approvalStatus);

        // For "Pending CM" and "Pending GM" statuses, show only the status text without date
        if (claim.approvalStatus === 'PENDING_CM' || claim.approvalStatus === 'PENDING_GM') {
            return escapeHtml(displayStatus);
        }

        // For all other statuses, include the date if available
        if (claim.approvalDate) {
            var approvalDate = new Date(claim.approvalDate);
            return escapeHtml(displayStatus) + ' (' + approvalDate.toLocaleDateString() + ')';
        }

        return escapeHtml(displayStatus);
    }

    /**
     * Build a single claim row HTML (internal helper)
     */
    function buildClaimRow(claim) {
        var row = '<tr data-claim-id="' + claim.id + '">';

        // Claims text
        row += '<td class="claim-text">' + escapeHtml(claim.claims || '') + '</td>';

        // Robustness column
        row += '<td>' + buildRobustnessCell(claim, robustnessCache) + '</td>';

        // Detail column
        row += '<td class="claim-detail">' + escapeHtml(claim.detail || '') + '</td>';

        // Framed Risk column
        row += '<td>' + buildRiskBadge(claim.framedRisk, '#6c757d') + '</td>';

        // Critical Risk column
        row += '<td>' + buildRiskBadge(claim.criticalRisk, '#6f42c1') + '</td>';

        // Fine and Penalty column
        row += '<td>' + buildFinePenaltyCell(claim, finePenaltyCache) + '</td>';

        // MKT Accepted status
        row += '<td class="mkt-accepted">' + buildMktAcceptedStatus(claim) + '</td>';

        // Approval status
        row += '<td class="approval-status">' + buildApprovalStatus(claim) + '</td>';

        // Actions column
        row += '<td>' + getActionButtonsForClaim(claim) + '</td>';

        row += '</tr>';
        return row;
    }

    /**
     * Reset claim form (internal module function)
     */
    function resetClaimForm() {
        // Reset individual fields manually since it's no longer a form
        $('#claim-text').val('');
        $('#claim-detail').val('');
        $('#robustness').val('');
        $('#framed-risk').val('');
        $('#critical-risk').val('');
        $('#fine-penalty').val('');
        $('#claim-image').val('');

        // No need to reset disabled state since fields are never disabled

        // Reset visual state
        $('#claims-text-group').show();
        $('#claims-image-group').hide();
        $('#claim-text').prop('required', true);
        $('#claim-image').prop('required', false);
        $('#claim-image-preview').empty();
        $('input[name="claimType"][value="text"]').prop('checked', true);
    }

    /**
     * Update existing claim in array (internal module function)
     */
    function updateClaim(claimId, updatedData) {
        var claimIndex = claimsData.findIndex(function(c) { return c.id == claimId; });

        if (claimIndex !== -1) {
            // Preserve existing properties and update with new data
            claimsData[claimIndex] = Object.assign({}, claimsData[claimIndex], updatedData);

            updateClaimsTable();
            return true;
        }
        return false;
    }

    /**
     * Remove claim by ID (internal module function)
     */
    function removeClaim(claimId) {
        claimsData = claimsData.filter(function(c) { return c.id != claimId; });
        updateClaimsTable();
    }

    /**
     * Debug function to check current reference data
     */
    function debugReferenceData() {
        console.log('=== REFERENCE DATA DEBUG ===');
        console.log('Robustness cache:', robustnessCache);
        console.log('Fine/penalty cache:', finePenaltyCache);
        console.log('Window robustness options:', window.robustnessOptions);
        console.log('Window fine/penalty options:', window.finePenaltyOptions);
        console.log('============================');

        return {
            robustnessCache: robustnessCache,
            finePenaltyCache: finePenaltyCache,
            windowRobustnessOptions: window.robustnessOptions,
            windowFinePenaltyOptions: window.finePenaltyOptions
        };
    }

    // Public API
    return {
        init: initializeClaimsData,
        getData: function() { return claimsData; },
        updateTable: updateClaimsTable,
        resetForm: resetClaimForm,
        updateClaim: updateClaim,
        removeClaim: removeClaim,
        getRobustnessCache: function() { return robustnessCache; },
        getFinePenaltyCache: function() { return finePenaltyCache; },
        loadReferenceData: loadReferenceData,
        enrichClaimsDataWithNames: enrichClaimsDataWithNames,
        refreshReferenceData: refreshReferenceData,
        debugReferenceData: debugReferenceData
    };
})();

$(document).ready(function() {
    // Initialize claims table on page load
    updateClaimsTable();
    
    // Claim type radio button change handler
    $('input[name="claimType"]').change(function() {
        var selectedType = $(this).val();

        if (selectedType === 'text') {
            $('#claims-text-group').show();
            $('#claims-image-group').hide();
            $('#claim-text').prop('required', true);
            $('#claim-image').prop('required', false);
        } else if (selectedType === 'image') {
            $('#claims-text-group').hide();
            $('#claims-image-group').show();
            $('#claim-text').prop('required', false);
            $('#claim-image').prop('required', true);
        }
    });

    // Add claim button handler
    $('#add-claim').click(function(e) {
        e.preventDefault();

        var claimType = $('input[name="claimType"]:checked').val();
        var claimsContent = '';
        var isValid = true;
        
        // Validate based on claim type
        if (claimType === 'text') {
            claimsContent = $('#claim-text').val().trim();
            if (!claimsContent) {
                jubiqAlert('warning', 'Please enter claims text');
                isValid = false;
            }
        } else if (claimType === 'image') {
            var fileInput = $('#claim-image')[0];
            if (!fileInput.files || fileInput.files.length === 0) {
                jubiqAlert('warning', 'Please select an image file');
                isValid = false;
            } else {
                claimsContent = fileInput.files[0].name; // Store filename for now
            }
        }
        
        // Validate other required fields
        var detail = $('#claim-detail').val().trim();
        var robustnessId = $('#robustness').val();
        var robustnessText = $('#robustness option:selected').text(); // Capture selected text
        var framedRisk = $('#framed-risk').val();
        var criticalRisk = $('#critical-risk').val();
        var fineAndPenaltyId = $('#fine-penalty').val();
        var fineAndPenaltyText = $('#fine-penalty option:selected').text(); // Capture selected text
        
        if (!detail) {
            jubiqAlert('warning', 'Please enter detail');
            isValid = false;
        }
        if (!robustnessId) {
            jubiqAlert('warning', 'Please select robustness');
            isValid = false;
        }
        // Validate that at least one risk field is selected
        if (!framedRisk && !criticalRisk) {
            jubiqAlert('warning', 'Please select either Framed Risk or Critical Risk');
            isValid = false;
        }
        if (!fineAndPenaltyId) {
            jubiqAlert('warning', 'Please select fine and penalty');
            isValid = false;
        }
        
        if (!isValid) {
            return;
        }

        // Create new claim (editing is now handled by modal only)
        var craIdValue = $('#claim-form input[name="craId"]').val();

        // Alternative method to get CRA ID if form selector fails
        if (!craIdValue || craIdValue === 'NOT_SET') {
            craIdValue = $('input[name="craId"]').val();
        }

        // Convert craId to integer to ensure proper data type
        craIdValue = parseInt(craIdValue, 10);
        if (isNaN(craIdValue)) {
            jubiqAlert('danger', 'Error: Invalid CRA ID. Cannot save claim.');
            return;
        }

        var claim = {
            id: Date.now(), // Temporary ID for client-side
            claimType: claimType,
            claims: claimsContent,
            detail: detail,
            robustnessId: robustnessId ? parseInt(robustnessId, 10) : null,
            robustnessName: robustnessId && robustnessText !== 'Select Robustness' ? robustnessText : null,
            framedRisk: framedRisk,
            criticalRisk: criticalRisk,
            fineAndPenaltyId: fineAndPenaltyId ? parseInt(fineAndPenaltyId, 10) : null,
            fineAndPenaltyName: fineAndPenaltyId && fineAndPenaltyText !== 'Select Fine and Penalty' ? fineAndPenaltyText : null,
            craId: craIdValue
        };

        // Add to claims array
        var claimsData = CraClaimsModule.getData();
        claimsData.push(claim);
        
        // Update the claims table
        updateClaimsTable();
        
        // Reset form
        resetClaimForm();
    });

    // Cancel claim form
    $('#cancel-claim').click(function() {
        resetClaimForm();
    });

    // Edit claim - Open modal popup
    $(document).on('click', '.edit-claim', function() {
        var claimId = $(this).data('claim-id');

        // Find claim in claimsData array
        var claimsData = CraClaimsModule.getData();
        var claim = claimsData.find(function(c) { return c.id == claimId; });
        if (!claim) {
            return;
        }

        // Reset modal to clean state first
        resetEditClaimModal();

        // Populate modal form with existing data
        $('#edit-claim-id').val(claimId);

        // Set claim type and manually trigger the UI update
        var claimType = claim.claimType || 'text'; // Default to text if undefined
        $('input[name="editClaimType"][value="' + claimType + '"]').prop('checked', true);

        // Manually update the UI based on claim type to ensure correct display
        if (claimType === 'text') {
            $('#edit-claims-text-group').css('display', 'block').show();
            $('#edit-claims-image-group').css('display', 'none').hide();
            $('#edit-claim-text').prop('required', true);
            $('#edit-claim-image').prop('required', false);
            // Set claims content for text type
            $('#edit-claim-text').val(claim.claims || '');
        } else if (claimType === 'image') {
            $('#edit-claims-text-group').css('display', 'none').hide();
            $('#edit-claims-image-group').css('display', 'block').show();
            $('#edit-claim-text').prop('required', false);
            $('#edit-claim-image').prop('required', true);
            // For image type, we can't restore the file input, but we can show the filename
            if (claim.claims) {
                $('#edit-claim-image-preview').html('<p>Current image: ' + claim.claims + '</p>');
            }
        }

        // Set other fields
        $('#edit-claim-detail').val(claim.detail || '');
        $('#edit-robustness').val(claim.robustnessId || '');
        $('#edit-framed-risk').val(claim.framedRisk || '');
        $('#edit-critical-risk').val(claim.criticalRisk || '');
        $('#edit-fine-penalty').val(claim.fineAndPenaltyId || '');

        // Handle legacy data with both fields populated - prioritize Framed Risk
        if (claim.framedRisk && claim.criticalRisk) {
            $('#edit-critical-risk').val('');
            jubiqAlert('warning', 'Legacy data detected: Critical Risk cleared to maintain data integrity');
        }

        // Show modal and ensure UI state is correct after modal is shown
        $('#edit-claim-modal').modal('show');

        // Add a small delay to ensure modal is fully rendered before final UI verification
        setTimeout(function() {
            // Double-check the UI state after modal is shown
            if (claimType === 'text') {
                $('#edit-claims-text-group').css('display', 'block').show();
                $('#edit-claims-image-group').css('display', 'none').hide();
            } else if (claimType === 'image') {
                $('#edit-claims-text-group').css('display', 'none').hide();
                $('#edit-claims-image-group').css('display', 'block').show();
            }
        }, 100);
    });

    // Remove claim
    $(document).on('click', '.remove-claim', function() {
        var claimId = $(this).data('claim-id');

        if (confirm('Are you sure you want to remove this claim?')) {
            // Remove from claimsData array using module method
            CraClaimsModule.removeClaim(claimId);

            // Reset form (since main form is now add-only, always reset)
            resetClaimForm();
        }
    });

    // Modal: Handle claim type change in edit modal
    $(document).on('change', 'input[name="editClaimType"]', function() {
        var claimType = $(this).val();

        if (claimType === 'text') {
            $('#edit-claims-text-group').css('display', 'block').show();
            $('#edit-claims-image-group').css('display', 'none').hide();
            $('#edit-claim-text').prop('required', true);
            $('#edit-claim-image').prop('required', false);
            // Clear image preview when switching to text
            $('#edit-claim-image-preview').empty();
        } else if (claimType === 'image') {
            $('#edit-claims-text-group').css('display', 'none').hide();
            $('#edit-claims-image-group').css('display', 'block').show();
            $('#edit-claim-text').prop('required', false);
            $('#edit-claim-image').prop('required', true);
        }
    });

    // Modal: Save edited claim
    $('#save-edit-claim').click(function() {
        var claimId = $('#edit-claim-id').val();
        if (!claimId) {
            jubiqAlert('warning', 'No claim selected for editing');
            return;
        }

        // Disable save button and show loading state
        var saveButton = $(this);
        var originalText = saveButton.text();
        saveButton.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');

        // Validate required fields based on claim type
        var isValid = true;
        var claimType = $('input[name="editClaimType"]:checked').val();
        var requiredFields = ['#edit-claim-detail', '#edit-robustness', '#edit-fine-penalty'];

        // Add claim content validation based on type
        if (claimType === 'text') {
            if (!$('#edit-claim-text').val().trim()) {
                $('#edit-claim-text').addClass('error');
                isValid = false;
            } else {
                $('#edit-claim-text').removeClass('error');
            }
        } else if (claimType === 'image') {
            // For image type, we'll assume the existing image is valid unless a new one is uploaded
            var fileInput = $('#edit-claim-image')[0];
            if (fileInput.files && fileInput.files.length > 0) {
                // New image uploaded, validate it
                var file = fileInput.files[0];
                if (!file.type.startsWith('image/')) {
                    $('#edit-claim-image').addClass('error');
                    isValid = false;
                    jubiqAlert('warning', 'Please select a valid image file');
                } else {
                    $('#edit-claim-image').removeClass('error');
                }
            }
        }

        // Validate other required fields
        requiredFields.forEach(function(field) {
            if (!$(field).val()) {
                $(field).addClass('error');
                isValid = false;
            } else {
                $(field).removeClass('error');
            }
        });

        // Validate that at least one risk field is selected
        var framedRisk = $('#edit-framed-risk').val();
        var criticalRisk = $('#edit-critical-risk').val();
        if (!framedRisk && !criticalRisk) {
            $('#edit-framed-risk').addClass('error');
            $('#edit-critical-risk').addClass('error');
            isValid = false;
        } else {
            $('#edit-framed-risk').removeClass('error');
            $('#edit-critical-risk').removeClass('error');
        }

        if (!isValid) {
            // Restore button state when validation fails
            saveButton.prop('disabled', false).text(originalText);
            jubiqAlert('warning', 'Please fill in all required fields and select either Framed Risk or Critical Risk');
            return;
        }

        // Get dropdown text values for display
        var robustnessText = $('#edit-robustness option:selected').text();
        var fineAndPenaltyText = $('#edit-fine-penalty option:selected').text();

        // Get form data based on claim type
        var claimType = $('input[name="editClaimType"]:checked').val();
        var claimsContent = '';

        if (claimType === 'text') {
            claimsContent = $('#edit-claim-text').val().trim();
        } else if (claimType === 'image') {
            // For image type, check if new file is uploaded or keep existing
            var fileInput = $('#edit-claim-image')[0];
            if (fileInput.files && fileInput.files.length > 0) {
                claimsContent = fileInput.files[0].name; // New image uploaded
            } else {
                // Keep existing image reference from original claim
                var originalClaim = CraClaimsModule.getData().find(function(c) { return c.id == claimId; });
                claimsContent = originalClaim ? originalClaim.claims : '';
            }
        }

        var updatedClaim = {
            id: claimId,
            claimType: claimType,
            claims: claimsContent,
            detail: $('#edit-claim-detail').val().trim(),
            robustnessId: $('#edit-robustness').val() ? parseInt($('#edit-robustness').val(), 10) : null,
            robustnessName: $('#edit-robustness').val() && robustnessText !== 'Select Robustness' ? robustnessText : null,
            framedRisk: framedRisk,
            criticalRisk: criticalRisk,
            fineAndPenaltyId: $('#edit-fine-penalty').val() ? parseInt($('#edit-fine-penalty').val(), 10) : null,
            fineAndPenaltyName: $('#edit-fine-penalty').val() && fineAndPenaltyText !== 'Select Fine and Penalty' ? fineAndPenaltyText : null
        };

        // Show loading state
        var loadingMessage = $('<div class="alert alert-info" style="position: fixed; top: 20px; right: 20px; z-index: 9999; width: 300px;">Saving claim to database...</div>');
        $('body').append(loadingMessage);

        // Get the original claim to preserve required fields like craId
        var originalClaim = CraClaimsModule.getData().find(function(c) { return c.id == claimId; });
        if (!originalClaim) {
            // Restore button state when original claim not found
            saveButton.prop('disabled', false).text(originalText);
            jubiqAlert('danger', 'Original claim not found. Cannot save changes.');
            loadingMessage.remove();
            return;
        }

        // Prepare API data - use the same format as backend updateClaim method
        var apiData = {
            id: parseInt(claimId, 10),
            craId: originalClaim.craId, // Required field from original claim
            claimType: updatedClaim.claimType,
            claims: updatedClaim.claims,
            detail: updatedClaim.detail,
            robustnessId: updatedClaim.robustnessId,
            framedRisk: updatedClaim.framedRisk,
            criticalRisk: updatedClaim.criticalRisk,
            fineAndPenaltyId: updatedClaim.fineAndPenaltyId,
            // Preserve existing workflow fields
            mktAcceptedStatus: originalClaim.mktAcceptedStatus,
            mktAcceptedDate: originalClaim.mktAcceptedDate,
            mktAcceptedBy: originalClaim.mktAcceptedBy,
            approvalStatus: originalClaim.approvalStatus,
            approvalDate: originalClaim.approvalDate,
            approverId: originalClaim.approverId,
            approverType: originalClaim.approverType
        };

        // Get authentication token from window (set by PHP session)
        var authToken = window.apiToken || window.accessToken || '';
        if (!authToken) {
            // Restore button state when auth token not available
            saveButton.prop('disabled', false).text(originalText);
            loadingMessage.remove();
            jubiqAlert('danger', 'Authentication token not available. Please refresh the page and try again.');
            return;
        }

        // Use RESTful PHP proxy endpoint (maintains architectural consistency)
        // Call backend API via PHP proxy (maintains same-origin while using REST semantics)
            // Call backend API via PHP proxy (maintains same-origin while using REST semantics)
            $.ajax({
                url: baseUrl + '/cra/cra-claims/' + claimId,
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(apiData),
                headers: {
                    'Authorization': 'Bearer ' + authToken,
                    'Accept': 'application/json'
                },
            success: function(response, _, xhr) {
                // Remove loading message and restore button
                loadingMessage.remove();
                saveButton.prop('disabled', false).text('Save');

                // Update local data after successful API call
                var localUpdateSuccess = CraClaimsModule.updateClaim(claimId, updatedClaim);

                // Highlight the updated row briefly
                setTimeout(function() {
                    var updatedRow = $('tr[data-claim-id="' + claimId + '"]');
                    if (updatedRow.length > 0) {
                        updatedRow.addClass('success').css('background-color', '#dff0d8');
                        setTimeout(function() {
                            updatedRow.removeClass('success').css('background-color', '');
                        }, 2000);
                    }
                }, 1200);

                // Show success notification using jubiq-alert system
                jubiqAlert('success', 'Claim saved to database successfully!');

                // Close modal immediately after successful save
                $('#edit-claim-modal').modal('hide');

                // Reset modal form
                resetEditClaimModal();
            },
            error: function(xhr, status, error) {
                // Remove loading message and restore button
                loadingMessage.remove();
                saveButton.prop('disabled', false).text('Save');

                // Handle different error scenarios for PHP proxy calls
                var errorText = error;

                if (xhr.status === 0) {
                    errorText = 'Cannot connect to PHP proxy. Please check your network connection.';
                } else if (xhr.status === 401) {
                    errorText = 'Authentication failed. Please refresh the page and try again.';
                } else if (xhr.status === 403) {
                    errorText = 'Access denied. You do not have permission to perform this action.';
                } else if (xhr.status === 404) {
                    errorText = 'Claim not found. It may have been deleted by another user.';
                } else if (xhr.responseText) {
                    try {
                        var errorResponse = JSON.parse(xhr.responseText);
                        errorText = errorResponse.message || errorResponse.error || error;
                    } catch (e) {
                        errorText = 'Server error (Status: ' + xhr.status + ')';
                    }
                }

                // Show error notification using jubiq-alert system
                jubiqAlert('danger', 'Failed to save claim: ' + errorText);
            }
        });
    });

    // Modal: Reset edit claim modal
    function resetEditClaimModal() {
        // Reset form fields manually since it's no longer a form
        $('#edit-claim-id').val('');
        $('#edit-claim-text').val('');
        $('#edit-claim-detail').val('');
        $('#edit-robustness').val('');
        $('#edit-framed-risk').val('');
        $('#edit-critical-risk').val('');
        $('#edit-fine-penalty').val('');
        $('#edit-claim-image').val('');

        // No need to reset disabled state since fields are never disabled

        // Reset claim type to text and ensure proper UI state
        $('input[name="editClaimType"][value="text"]').prop('checked', true);
        $('input[name="editClaimType"][value="image"]').prop('checked', false);

        // Ensure text group is shown and image group is hidden
        $('#edit-claims-text-group').css('display', 'block').show();
        $('#edit-claims-image-group').css('display', 'none').hide();

        // Reset required attributes
        $('#edit-claim-text').prop('required', true);
        $('#edit-claim-image').prop('required', false);

        // Clear preview and error states
        $('#edit-claim-image-preview').empty();
        $('#edit-claim-modal .form-control').removeClass('error');
    }

    // Modal: Reset modal when closed
    $('#edit-claim-modal').on('hidden.bs.modal', function() {
        resetEditClaimModal();
    });

    // Modal: Handle ESC key to close modal
    $(document).keyup(function(e) {
        if (e.keyCode === 27 && $('#edit-claim-modal').hasClass('in')) {
            $('#edit-claim-modal').modal('hide');
        }
    });

    // Modal: Handle image preview in edit modal
    $('#edit-claim-image').change(function() {
        var file = this.files[0];
        var preview = $('#edit-claim-image-preview');

        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                preview.html('<img src="' + e.target.result + '" style="max-width: 200px; max-height: 200px;" class="img-thumbnail">');
            };
            reader.readAsDataURL(file);
        } else {
            preview.empty();
        }
    });

    // Modal: Validate form fields on blur
    $('#edit-claim-modal .form-control').blur(function() {
        if ($(this).prop('required') && !$(this).val()) {
            $(this).addClass('error');
        } else {
            $(this).removeClass('error');
        }
    });

    // Add New Claim: Last selection wins behavior for Framed Risk and Critical Risk
    $('#framed-risk').change(function() {
        if ($(this).val()) {
            // If Critical Risk was already selected, clear it and show notification
            if ($('#critical-risk').val()) {
                $('#critical-risk').val('');
                jubiqAlert('info', 'Critical Risk cleared - Framed Risk selected');
            }
        }
    });

    $('#critical-risk').change(function() {
        if ($(this).val()) {
            // If Framed Risk was already selected, clear it and show notification
            if ($('#framed-risk').val()) {
                $('#framed-risk').val('');
                jubiqAlert('info', 'Framed Risk cleared - Critical Risk selected');
            }
        }
    });

    // Edit Claim Modal: Last selection wins behavior for Framed Risk and Critical Risk
    $('#edit-framed-risk').change(function() {
        if ($(this).val()) {
            // If Critical Risk was already selected, clear it and show notification
            if ($('#edit-critical-risk').val()) {
                $('#edit-critical-risk').val('');
                jubiqAlert('info', 'Critical Risk cleared - Framed Risk selected');
            }
        }
    });

    $('#edit-critical-risk').change(function() {
        if ($(this).val()) {
            // If Framed Risk was already selected, clear it and show notification
            if ($('#edit-framed-risk').val()) {
                $('#edit-framed-risk').val('');
                jubiqAlert('info', 'Framed Risk cleared - Critical Risk selected');
            }
        }
    });

    // Image file preview
    $('#claim-image').change(function() {
        var file = this.files[0];
        var preview = $('#claim-image-preview');
        preview.empty();
        
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                preview.html('<img src="' + e.target.result + '" style="max-width: 200px; max-height: 200px;" class="img-thumbnail">');
            };
            reader.readAsDataURL(file);
        }
    });

    // Robustness link click handler
    $(document).on('click', '.robustness-link', function(e) {
        e.preventDefault();
        var robustnessId = $(this).data('robustness-id');
        showRobustnessDetails(robustnessId);
    });

    // Fine and Penalty link click handler
    $(document).on('click', '.fine-penalty-link', function(e) {
        e.preventDefault();
        var finePenaltyId = $(this).data('fine-penalty-id');
        showFinePenaltyDetails(finePenaltyId);
    });

    // Marketing acceptance button handlers
    $(document).on('click', '.accept-claim, .reject-claim', function(e) {
        e.preventDefault();

        // Check if button is disabled
        if ($(this).hasClass('disabled') || $(this).prop('disabled')) {
            // Show user-friendly message using jubiqAlert if available, otherwise alert
            var message = 'These buttons are only available when the CRA status is "Wait for Approval".';
            if (typeof jubiqAlert === 'function') {
                jubiqAlert('warning', message);
            } else {
                alert(message);
            }
            return false;
        }

        var claimId = $(this).data('claim-id');
        var action = $(this).hasClass('accept-claim') ? 'accepted' : 'not_accepted';
        handleMarketingAcceptance(claimId, action);
    });
});

// Legacy support - these functions now delegate to the module
function loadReferenceData() {
    return CraClaimsModule.loadReferenceData();
}

function enrichClaimsDataWithNames() {
    return CraClaimsModule.enrichClaimsDataWithNames();
}




/**
 * Get role-based action buttons for a claim
 */
function getActionButtonsForClaim(claim) {
    var buttons = '';
    var userGroup = window.userGroupName || '';
    var currentPage = window.currentPage || '';

    // SCI Groups on process page: Edit and Remove buttons
    if ((userGroup === 'SCI Manager' || userGroup === 'SCI Staff') && currentPage === 'process') {
        buttons += '<button class="btn btn-xs btn-primary edit-claim" data-claim-id="' + claim.id + '">Edit</button> ';
        buttons += '<button class="btn btn-xs btn-danger remove-claim" data-claim-id="' + claim.id + '">Remove</button>';
    }
    // Marketing Groups on update page: Accepted and Not Accepted buttons
    else if (['Marketing', 'CPD', 'ACD', 'PPD', 'LUXE'].indexOf(userGroup) !== -1 && currentPage === 'update') {
        if (!claim.mktAcceptedStatus) {
            // Check if buttons should be enabled based on CRA status
            var craStatus = window.craStatus || '';
            var buttonsEnabled = craStatus === 'WAIT_FOR_APPROVAL';
            var disabledClass = buttonsEnabled ? '' : ' disabled';
            var disabledAttr = buttonsEnabled ? '' : ' disabled';
            var titleAttr = buttonsEnabled ? '' : ' title="Available only when CRA status is \'Wait for Approval\'"';

            buttons += '<button class="btn btn-xs btn-success accept-claim' + disabledClass + '" data-claim-id="' + claim.id + '"' + disabledAttr + titleAttr + '>Accepted</button> ';
            buttons += '<button class="btn btn-xs btn-warning reject-claim' + disabledClass + '" data-claim-id="' + claim.id + '"' + disabledAttr + titleAttr + '>Not Accepted</button>';
        } else {
            // Show current status if already decided (handle both uppercase and lowercase)
            var status = claim.mktAcceptedStatus.toLowerCase();
            var statusClass = status === 'accepted' ? 'btn-success' : 'btn-warning';
            var displayText = status === 'accepted' ? 'Accepted' : 'Not Accepted';
            buttons += '<span class="btn btn-xs ' + statusClass + ' disabled">' + displayText + '</span>';
        }
    }
    // Default: Edit and Remove for backward compatibility
    else {
        buttons += '<button class="btn btn-xs btn-primary edit-claim" data-claim-id="' + claim.id + '">Edit</button> ';
        buttons += '<button class="btn btn-xs btn-danger remove-claim" data-claim-id="' + claim.id + '">Remove</button>';
    }

    return buttons;
}

/**
 * Function to collect claims data for form submission
 * Gets the most up-to-date data from the module (includes modal edits)
 */
function getClaimsDataJson() {
    var currentClaimsData = CraClaimsModule.getData();
    return JSON.stringify(currentClaimsData);
}

/**
 * Show robustness details in popup modal using cached data or backend-enriched data
 */
function showRobustnessDetails(robustnessId) {
    // Validate robustness ID
    if (!robustnessId || isNaN(robustnessId)) {
        jubiqAlert('info', 'Invalid robustness ID: ' + robustnessId);
        return;
    }



    // Get data from cache first (using module API)
    var robustnessCache = CraClaimsModule.getRobustnessCache();
    var data = robustnessCache[robustnessId];

    // If not in cache, try to find it in the claims data (backend-enriched)
    if (!data) {
        var claimsData = CraClaimsModule.getData();
        var claimWithRobustness = claimsData.find(function(claim) {
            return claim.robustnessId == robustnessId && claim.robustnessName;
        });
        if (claimWithRobustness) {
            data = {
                id: robustnessId,
                itemName: claimWithRobustness.robustnessName,
                description: 'Details available to SCI team only'
            };
        }
    }

    if (data) {
        var modalHtml = '<div class="modal fade" id="robustnessModal" tabindex="-1" role="dialog" aria-labelledby="robustnessModalLabel">' +
            '<div class="modal-dialog" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<h4 class="modal-title" id="robustnessModalLabel">Robustness Details</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<table class="table table-bordered">' +
            '<tr><td><strong>ID:</strong></td><td>' + robustnessId + '</td></tr>' +
            '<tr><td><strong>Item Name:</strong></td><td>' + (data.itemName || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Description:</strong></td><td>' + (data.description || 'N/A') + '</td></tr>' +
            '</table>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
            '</div>' +
            '</div></div></div>';

        // Remove existing modal and add new one
        $('#robustnessModal').remove();
        $('body').append(modalHtml);
        $('#robustnessModal').modal('show');
    } else {


        // Show a user-friendly modal indicating data is not available
        var modalHtml = '<div class="modal fade" id="robustnessModal" tabindex="-1" role="dialog" aria-labelledby="robustnessModalLabel">' +
            '<div class="modal-dialog" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<h4 class="modal-title" id="robustnessModalLabel">Robustness Details</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<table class="table table-bordered">' +
            '<tr><td><strong>ID:</strong></td><td>' + robustnessId + '</td></tr>' +
            '<tr><td><strong>Status:</strong></td><td>Robustness details are currently being loaded</td></tr>' +
            '<tr><td><strong>Note:</strong></td><td>Please refresh the page or contact support if this issue persists</td></tr>' +
            '</table>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
            '</div>' +
            '</div></div></div>';

        // Remove existing modal and add new one
        $('#robustnessModal').remove();
        $('body').append(modalHtml);
        $('#robustnessModal').modal('show');
    }
}

/**
 * Show fine and penalty details in popup modal using cached data or backend-enriched data
 */
function showFinePenaltyDetails(finePenaltyId) {
    // Validate fine and penalty ID
    if (!finePenaltyId || isNaN(finePenaltyId)) {
        jubiqAlert('warning', 'Invalid fine and penalty ID: ' + finePenaltyId);
        return;
    }



    // Get data from cache first (using module API)
    var finePenaltyCache = CraClaimsModule.getFinePenaltyCache();
    var data = finePenaltyCache[finePenaltyId];

    // If not in cache, try to find it in the claims data (backend-enriched)
    if (!data) {
        var claimsData = CraClaimsModule.getData();
        var claimWithFinePenalty = claimsData.find(function(claim) {
            return claim.fineAndPenaltyId == finePenaltyId && claim.fineAndPenaltyName;
        });
        if (claimWithFinePenalty) {
            data = {
                id: finePenaltyId,
                itemName: claimWithFinePenalty.fineAndPenaltyName,
                descriptionOfActs: 'Details available to SCI team only',
                penaltiesSanctionsApplied: 'Details available to SCI team only',
                otherRemedies: 'Details available to SCI team only',
                legalBackground: 'Details available to SCI team only'
            };
        }
    }

    if (data) {
        var modalHtml = '<div class="modal fade" id="finePenaltyModal" tabindex="-1" role="dialog" aria-labelledby="finePenaltyModalLabel">' +
            '<div class="modal-dialog modal-lg" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<h4 class="modal-title" id="finePenaltyModalLabel">Fine and Penalty Details</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<table class="table table-bordered">' +
            '<tr><td><strong>ID:</strong></td><td>' + finePenaltyId + '</td></tr>' +
            '<tr><td><strong>Item Name:</strong></td><td>' + (data.itemName || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Description of Acts:</strong></td><td>' + (data.descriptionOfActs || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Penalties/Sanctions Applied:</strong></td><td>' + (data.penaltiesSanctionsApplied || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Other Remedies:</strong></td><td>' + (data.otherRemedies || 'N/A') + '</td></tr>' +
            '<tr><td><strong>Legal Background:</strong></td><td>' + (data.legalBackground || 'N/A') + '</td></tr>' +
            '</table>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
            '</div>' +
            '</div></div></div>';

        // Remove existing modal and add new one
        $('#finePenaltyModal').remove();
        $('body').append(modalHtml);
        $('#finePenaltyModal').modal('show');
    } else {


        // Show a user-friendly modal indicating data is not available
        var modalHtml = '<div class="modal fade" id="finePenaltyModal" tabindex="-1" role="dialog" aria-labelledby="finePenaltyModalLabel">' +
            '<div class="modal-dialog modal-lg" role="document">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '<h4 class="modal-title" id="finePenaltyModalLabel">Fine and Penalty Details</h4>' +
            '</div>' +
            '<div class="modal-body">' +
            '<table class="table table-bordered">' +
            '<tr><td><strong>ID:</strong></td><td>' + finePenaltyId + '</td></tr>' +
            '<tr><td><strong>Status:</strong></td><td>Fine and penalty details are currently being loaded</td></tr>' +
            '<tr><td><strong>Note:</strong></td><td>Please refresh the page or contact support if this issue persists</td></tr>' +
            '</table>' +
            '</div>' +
            '<div class="modal-footer">' +
            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
            '</div>' +
            '</div></div></div>';

        // Remove existing modal and add new one
        $('#finePenaltyModal').remove();
        $('body').append(modalHtml);
        $('#finePenaltyModal').modal('show');
    }
}

/**
 * Handle marketing acceptance/rejection of claims
 */
function handleMarketingAcceptance(claimId, action) {
    if (!confirm('Are you sure you want to ' + action.replace('_', ' ') + ' this claim?')) {
        return;
    }

    // Get the claim data to determine risk level
    var claimsData = CraClaimsModule.getData();
    var claim = claimsData.find(function(c) { return c.id == claimId; });
    if (!claim) {
        jubiqAlert('warning', 'Claim not found');
        return;
    }



    // Proceed with the API call via PHP proxy
    performMktAcceptCall(claimId, action, claim);
}

/**
 * Note: API connectivity test removed since PHP proxy is working correctly
 */

/**
 * Perform the actual mkt-accept API call via PHP proxy
 */
function performMktAcceptCall(claimId, action, claim) {
    $.ajax({
        url: baseUrl + '/cra/apiMktAccept/' + claimId,
        type: 'POST',
        data: {
            mktAcceptedStatus: action.toUpperCase()
        },
        success: function() {
            handleSuccessfulMktAccept(claimId, action, claim);
        },
        error: function(xhr, _, error) {
            console.error('Error updating claim acceptance status:', error);

            // Try to parse error response
            try {
                var errorResponse = JSON.parse(xhr.responseText);
                jubiqAlert('danger', 'Error updating claim acceptance status: ' + (errorResponse.error || error));
            } catch (e) {
                jubiqAlert('danger', 'Error updating claim acceptance status: ' + error + ' (Status: ' + xhr.status + ')');
            }
        }
    });
}

/**
 * Note: Alternative API call methods removed since we're now using PHP proxy
 * which handles the backend communication directly.
 */

/**
 * Handle successful marketing acceptance (common logic)
 */
function handleSuccessfulMktAccept(claimId, action, claim) {
    // Update the claim in claimsData
    if (claim) {
        claim.mktAcceptedStatus = action.toUpperCase();
        claim.mktAcceptedDate = new Date().toISOString();
        claim.mktAcceptedBy = window.userId || null;
    }

    // Step 2: If action is "accepted", call approve endpoint with risk-based logic
    if (action.toLowerCase() === 'accepted') {
        handleApprovalWorkflow(claimId, claim);
    } else {
        // For "not_accepted", only step 1 is needed
        updateClaimsTable();
    }
}

/**
 * Handle the approval workflow based on risk assessment
 */
function handleApprovalWorkflow(claimId, claim) {
    // Determine approval status and approver type based on risk level
    var approvalStatus;
    var baseApproverType = window.userGroupName || 'MKT'; // Default to Marketing if not available
    var approverType = baseApproverType; // Will be modified based on critical risk level

    // Risk level determination logic
    if (claim.framedRisk && !claim.criticalRisk) {
        // Framed Risk claims
        approvalStatus = 'APPROVED';
    } else if (claim.criticalRisk) {
        // Critical Risk claims - check specific levels
        var criticalLevel = claim.criticalRisk.toUpperCase();
        if (criticalLevel === 'IIIC' || criticalLevel === 'ID' || criticalLevel === 'IID') {
            approvalStatus = 'PENDING_GM';
            // Add GM prefix for these critical risk levels
            approverType = 'GM-' + baseApproverType;
        } else if (criticalLevel === 'IIID') {
            approvalStatus = 'PENDING_CM';
            // Add CM prefix for IIID critical risk level
            approverType = 'CM';
        } else {
            // Default for other critical risk levels
            approvalStatus = 'PENDING_GM';
            // Add GM prefix for other critical risk levels
            approverType = 'GM-' + baseApproverType;
        }
    } else {
        // No risk assessment available - default to approved
        approvalStatus = 'APPROVED';
    }

    // Step 2: Call approve endpoint via PHP proxy
    $.ajax({
        url: baseUrl + '/cra/apiApprove/' + claimId,
        type: 'POST',
        data: {
            approvalStatus: approvalStatus,
            approverType: approverType
        },
        success: function() {
            // Update the claim in claimsData
            claim.approvalStatus = approvalStatus;
            claim.approvalDate = new Date().toISOString();
            claim.approverId = window.userId || null;
            claim.approverType = approverType;

            // Refresh the table
            updateClaimsTable();
        },
        error: function(xhr, _, error) {
            console.error('Error in approval workflow:', error);

            // Try to parse error response
            try {
                var errorResponse = JSON.parse(xhr.responseText);
                jubiqAlert('warning', 'Claim was accepted but approval workflow failed: ' + (errorResponse.error || error));
            } catch (e) {
                jubiqAlert('warning', 'Claim was accepted but approval workflow failed: ' + error);
            }

            // Still refresh the table since step 1 succeeded
            updateClaimsTable();
        }
    });
}

/**
 * Format approval status for display
 */
function formatApprovalStatus(status) {
    if (!status) return '';

    switch (status.toUpperCase()) {
        case 'APPROVED':
            return 'Approved';
        case 'PENDING_GM':
            return 'Pending GM';
        case 'PENDING_CM':
            return 'Pending CM';
        case 'NOT_APPROVED':
            return 'Not Approved';
        default:
            return status;
    }
}

// Expose functions globally for main form submission and external access
window.getClaimsDataJson = getClaimsDataJson;
window.initializeClaimsData = CraClaimsModule.init;
window.updateClaimsTable = CraClaimsModule.updateTable;
window.resetClaimForm = CraClaimsModule.resetForm;

// Legacy support for direct access
Object.defineProperty(window, 'claimsData', {
    get: function() { return CraClaimsModule.getData(); }
});
