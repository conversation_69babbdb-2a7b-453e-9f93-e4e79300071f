<?php foreach($histories as $history):?>
<a class="list-group-item" href="#">
	<span class="clear block">
		<?php
			// Display different icons and labels for different object types
			if ($history->objectName === 'CRA_CLAIMS') {
				echo '<i class="fa fa-list-alt text-info"></i> ';
				echo '<strong>Claims:</strong> ';
			} else {
				echo '<i class="fa fa-file-text text-primary"></i> ';
				echo '<strong>CRA Request:</strong> ';
			}
			echo HistoryService::view($history);
		?>
		<?php if(in_array($history->action,array('reject','recall'))):?>
		<br>
		<i><?php echo isset($history->reason)?$history->reason:"";?></i>
		<?php else:?>
			<?php if(isset($history->newValue)):?>
			<?php
				// Use appropriate comparison method based on object type
				if ($history->objectName === 'CRA_CLAIMS') {
					$values = HistoryService::compareClaimsHistory($history);
					$fieldDefinitions = CraService::$list_claims_fields;
				} else {
					$values = HistoryService::compareCraHistory($history);
					$fieldDefinitions = CraService::$list_all_fields;
				}
			?>
			<?php if(sizeof($values)>0):?>
			<table class="table table-editable table-hover table-striped table-bordered n-m-b m-t">
				<thead class="text-nowrap">
					<th>Field</th>
					<th>Old Value</th>
					<th>New Value</th>
				</thead>
				<tbody>
					<?php foreach($values as $name=>$compare):?>
					<?php if(isset($fieldDefinitions[$name])):?>
					<tr>
						<td><?php echo $fieldDefinitions[$name];?></td>
						<td><?php echo $compare['old'];?></td>
						<td><?php echo $compare['new'];?></td>
					</tr>
					<?php endif;?>
					<?php endforeach;?>
				</tbody>
			</table>
			<?php endif;?>
			<?php endif;?>
		<?php endif;?>
		<small class="text-muted clear block"><?php echo date("H:i d-m-Y",$history->created/1000);?></small>
	</span>
</a>
<?php endforeach;?>
