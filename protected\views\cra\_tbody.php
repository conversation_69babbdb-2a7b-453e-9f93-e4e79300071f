<?php
// Safety check: ensure $items is an array
if (!is_array($items)) {
    $items = array();
}

foreach($items as $item):
	// Skip null or invalid items
	if (!$item || !isset($item->id)) {
		continue;
	}
?>
<tr data-id="<?php echo $item->id;?>">
	<td class="text-center">
		<div class="checkbox no-margin">
			<label class="ui-checks">
				<input type="checkbox" value="<?php echo $item->id;?>" class="check-item">
				<i></i>
			</label>
		</div>
	</td>

	<!-- CRA Number (Database ID) -->
	<td class="column-id read-cra">
		<?php echo $item->id; ?>
	</td>

	<!-- Content Files -->
	<td class="column-contentFiles read-cra">
			<?php
                			$contentFiles = array();
                			if(isset($item->contentFiles)){
                				$contentFiles = (array)$item->contentFiles;
                				$i=0;
                			}
                		?>
                		<?php if(sizeof($contentFiles)>1):?>
                		<?php
                			$fileIds = array();
                			foreach($contentFiles as $file_id => $file_name){
                				$fileIds[] = $file_id;
                			}
                		?>
                		<a target="_blank" href="<?php echo Yii::app()->createUrl("file/multiDownload",array('fileIds'=>implode(',',$fileIds)));?>">Download all</a>
                		<ul>
                		<?php foreach($contentFiles as $file_id => $file_name):?>
                			<li>
                				<a target="_blank" href="<?php echo Yii::app()->createUrl("file/download",array('id'=>$file_id));?>"><?php echo $file_name;?></a>
                			</li>
                			<?php $i++;?>
                		<?php endforeach;?>
                		</ul>
                		<?php else:?>
                			<?php foreach($contentFiles as $file_id => $file_name):?>
                				<a target="_blank" href="<?php echo Yii::app()->createUrl("file/download",array('id'=>$file_id));?>"><?php echo $file_name;?></a>
                			<?php endforeach;?>
                		<?php endif;?>

	</td>

	<!-- Date of Submitting -->
	<td class="column-dateOfSubmitting read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfSubmitting) ? $item->dateOfSubmitting : null, 'Y-m-d'); ?>
	</td>

	<!-- Date of Requesting Proof Document -->
	<td class="column-dateOfRequestingProofDocument read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfRequestingProofDocument) ? $item->dateOfRequestingProofDocument : null, 'Y-m-d'); ?>
	</td>

	<!-- Date of Receiving Proof Document -->
	<td class="column-dateOfReceivingProofDocument read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfReceivingProofDocument) ? $item->dateOfReceivingProofDocument : null, 'Y-m-d'); ?>
	</td>

	<!-- Proof Documents -->
	<td class="column-proofDocuments read-cra">
    		<?php
        			$proofDocuments = array();
        			if(isset($item->proofDocuments)){
        				$proofDocuments = (array)$item->proofDocuments;
        				$i=0;
        			}
        		?>
        		<?php if(sizeof($proofDocuments)>1):?>
        		<?php
        			$fileIds = array();
        			foreach($proofDocuments as $file_id => $file_name){
        				$fileIds[] = $file_id;
        			}
        		?>
        		<a target="_blank" href="<?php echo Yii::app()->createUrl("file/multiDownload",array('fileIds'=>implode(',',$fileIds)));?>">Download all</a>
        		<ul>
        		<?php foreach($proofDocuments as $file_id => $file_name):?>
        			<li>
        				<a target="_blank" href="<?php echo Yii::app()->createUrl("file/download",array('id'=>$file_id));?>"><?php echo $file_name;?></a>
        			</li>
        			<?php $i++;?>
        		<?php endforeach;?>
        		</ul>
        		<?php else:?>
        			<?php foreach($proofDocuments as $file_id => $file_name):?>
        				<a target="_blank" href="<?php echo Yii::app()->createUrl("file/download",array('id'=>$file_id));?>"><?php echo $file_name;?></a>
        			<?php endforeach;?>
        		<?php endif;?>

	</td>

	<!-- Date of STRAD Risk Assessment -->
	<td class="column-dateOfStradRiskAssessment read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfStradRiskAssessment) ? $item->dateOfStradRiskAssessment : null, 'Y-m-d'); ?>
	</td>

	<!-- Date of Completed -->
	<td class="column-dateOfCompleted read-cra">
		<?php echo CraService::formatDate(isset($item->dateOfCompleted) ? $item->dateOfCompleted : null, 'Y-m-d'); ?>
	</td>

	<!-- Exposition (Combined Level and Detail) -->
	<td class="column-exposition read-cra">
		<?php
		$expositionLevel = isset($item->expositionLevel) && !empty($item->expositionLevel) ? $item->expositionLevel : '';
		$expositionDetail = isset($item->expositionDetail) && !empty($item->expositionDetail) ? $item->expositionDetail : '';

		if (!empty($expositionLevel) && !empty($expositionDetail)) {
			echo $expositionLevel . ' - ' . $expositionDetail;
		} elseif (!empty($expositionLevel)) {
			echo $expositionLevel;
		} elseif (!empty($expositionDetail)) {
			echo $expositionDetail;
		} else {
			echo '-';
		}
		?>
	</td>

	<!-- Creator Email -->
	<td class="column-creatorEmail read-cra">
		<?php echo isset($item->creatorEmail) && !empty($item->creatorEmail) ? $item->creatorEmail : '-'; ?>
	</td>

	<!-- Follower Email -->
	<td class="column-followerEmail read-cra">
		<?php echo isset($item->followerEmail) && !empty($item->followerEmail) ? $item->followerEmail : '-'; ?>
	</td>

	<!-- Assignee Email -->
	<td class="column-assigneeEmail read-cra">
		<?php echo isset($item->assigneeEmail) && !empty($item->assigneeEmail) ? $item->assigneeEmail : '-'; ?>
	</td>

	<!-- Creator Full Name -->
	<td class="column-creatorFullName read-cra">
		<?php echo isset($item->creatorFullName) && !empty($item->creatorFullName) ? $item->creatorFullName : '-'; ?>
	</td>

	<!-- Follower Full Name -->
	<td class="column-followerFullName read-cra">
		<?php echo isset($item->followerFullName) && !empty($item->followerFullName) ? $item->followerFullName : '-'; ?>
	</td>

	<!-- Assignee Full Name -->
	<td class="column-assigneeFullName read-cra">
		<?php echo isset($item->assigneeFullName) && !empty($item->assigneeFullName) ? $item->assigneeFullName : '-'; ?>
	</td>

	<!-- Reference Files -->
	<td class="column-referencesFiles read-cra">
	<?php
			$referencesFiles = array();
			if(isset($item->referencesFiles)){
				$referencesFiles = (array)$item->referencesFiles;
				$i=0;
			}
		?>
		<?php if(sizeof($referencesFiles)>1):?>
		<?php
			$fileIds = array();
			foreach($referencesFiles as $file_id => $file_name){
				$fileIds[] = $file_id;
			}
		?>
		<a target="_blank" href="<?php echo Yii::app()->createUrl("file/multiDownload",array('fileIds'=>implode(',',$fileIds)));?>">Download all</a>
		<ul>
		<?php foreach($referencesFiles as $file_id => $file_name):?>
			<li>
				<a target="_blank" href="<?php echo Yii::app()->createUrl("file/download",array('id'=>$file_id));?>"><?php echo $file_name;?></a>
			</li>
			<?php $i++;?>
		<?php endforeach;?>
		</ul>
		<?php else:?>
			<?php foreach($referencesFiles as $file_id => $file_name):?>
				<a target="_blank" href="<?php echo Yii::app()->createUrl("file/download",array('id'=>$file_id));?>"><?php echo $file_name;?></a>
			<?php endforeach;?>
		<?php endif;?>
	</td>


	<td class="column-requestIds read-cra">
		<?php
			$requestIds = isset($item->requestIds)?$item->requestIds:array();
		?>
		<div style='width: 500px;'>
		<ol>
			<?php foreach($requestIds as $requestId):?>
						<?php
                				$notification = NotificationService::get($requestId);
                				?>
				<li>Request No <?php echo $notification->id; ?> (<?php echo $notification->brandName;?> - <?php echo $notification->productName;?>)</li>
			<?php endforeach;?>
		</ol>
		</div>
	</td>


	<!-- Status -->
	<td class="column-status read-cra">
		<?php echo isset($item->status) && !empty($item->status) ? $item->status : '-'; ?>
	</td>

	<!-- STRAD Risk Assessment Files -->
	<td class="column-stradRiskAssessmentFiles read-cra">
			<?php
                		$stradRiskAssessmentFiles = array();
                		if(isset($item->stradRiskAssessmentFiles)){
                			$stradRiskAssessmentFiles = (array)$item->stradRiskAssessmentFiles;
                			$i=0;
                		}
                	?>
                	<?php if(sizeof($stradRiskAssessmentFiles)>1):?>
                	<?php
                		$fileIds = array();
                		foreach($stradRiskAssessmentFiles as $file_id => $file_name){
                			$fileIds[] = $file_id;
                		}
                	?>
                	<a target="_blank" href="<?php echo Yii::app()->createUrl("file/multiDownload",array('fileIds'=>implode(',',$fileIds)));?>">Download all</a>
                	<ul>
                	<?php foreach($stradRiskAssessmentFiles as $file_id => $file_name):?>
                		<li>
                			<a target="_blank" href="<?php echo Yii::app()->createUrl("file/download",array('id'=>$file_id));?>"><?php echo $file_name;?></a>
                		</li>
                		<?php $i++;?>
                	<?php endforeach;?>
                	</ul>
                	<?php else:?>
                		<?php foreach($stradRiskAssessmentFiles as $file_id => $file_name):?>
                			<a target="_blank" href="<?php echo Yii::app()->createUrl("file/download",array('id'=>$file_id));?>"><?php echo $file_name;?></a>
                		<?php endforeach;?>
                	<?php endif;?>

	</td>

	<!-- Timeline -->
	<td class="column-timeline read-cra">
		<?php echo CraService::formatDate(isset($item->timeline) ? $item->timeline : null, 'Y-m-d'); ?>
	</td>
</tr>
<?php
endforeach;
?>
