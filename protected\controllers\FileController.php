<?php

class FileController extends Controller
{
	public $layout = 'main';

	/**
	 * @var CActiveRecord the currently loaded data model instance.
	 */
	private $_model;

	/**
	 * @return array action filters
	 */
	public function filters()
	{
		return array(
			'api'
		);
	}

	/**
	 * Specifies the access control rules.
	 * This method is used by the 'accessControl' filter.
	 * @return array access control rules
	 */
	public function accessApiRules()
	{
		return array(
			array(
				'allow',
				'actions' => array('download', 'multiDownload', 'streamUrl'),
				'users' => array('@'),
			),
			array(
				'deny',
				'users' => array('*'),
			),
		);
	}

	public function actionStreamUrl($id)
	{
		$file = FileService::download($id);
		$file_array = explode("\n\r", $file, 2);
		$header_array = explode("\n", $file_array[0]);
		foreach ($header_array as $header_value) {
			$header_pieces = explode(':', $header_value);
			if (count($header_pieces) == 2) {
				$tmp_headers[$header_pieces[0]] = trim($header_pieces[1]);
			}
		}

		$headers = [];
		foreach ($tmp_headers as $key => $value) {
			$headers[strtolower($key)] = $value;
		}

		$filename = substr($headers['content-disposition'], (9 + strpos($headers['content-disposition'], "filename=")));

		$filename = trim($filename, '"');

		$extension = pathinfo($filename, PATHINFO_EXTENSION);

		// Map extension sang mime-type
		$mimeTypes = [
			'pdf' => 'application/pdf',
			'jpg' => 'image/jpeg',
			'jpeg' => 'image/jpeg',
			'png' => 'image/png',
			'gif' => 'image/gif',
			'doc' => 'application/msword',
			'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'xls' => 'application/vnd.ms-excel',
			'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		];

		$mimeType = isset($mimeTypes[$extension]) ? $mimeTypes[$extension] : 'application/octet-stream';


		header('Content-Type: ' . $mimeType);
		header('Content-Disposition: attachment; filename="' . $filename . '"');
		echo substr($file_array[1], 1);

		exit;
	}

	public function actionDownload($id)
	{
		$file = FileService::download($id);
		$file_array = explode("\n\r", $file, 2);
		$header_array = explode("\n", $file_array[0]);
		foreach ($header_array as $header_value) {
			$header_pieces = explode(':', $header_value);
			if (count($header_pieces) == 2) {
				$tmp_headers[$header_pieces[0]] = trim($header_pieces[1]);
			}
		}

		$headers = [];
		foreach ($tmp_headers as $key => $value) {
			$headers[strtolower($key)] = $value;
		}

		$filename = substr($headers['content-disposition'], (9 + strpos($headers['content-disposition'], "filename=")));

		$filename = trim($filename, '"');

		$extension = pathinfo($filename, PATHINFO_EXTENSION);

		// Map extension sang mime-type
		$mimeTypes = [
			'pdf' => 'application/pdf',
			'jpg' => 'image/jpeg',
			'jpeg' => 'image/jpeg',
			'png' => 'image/png',
			'gif' => 'image/gif',
			'doc' => 'application/msword',
			'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'xls' => 'application/vnd.ms-excel',
			'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		];

		$mimeType = isset($mimeTypes[$extension]) ? $mimeTypes[$extension] : 'application/octet-stream';

		// Check loại file
		$isOffice = in_array($extension, ['docx', 'doc', 'xlsx', 'xls']);
		$isViewable = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'pdf']);

		if ($isViewable) {
			header('Content-Type: ' . $mimeType);
			header('Content-Disposition: inline; filename="' . $filename . '"');
			echo substr($file_array[1], 1);
		} elseif ($isOffice) {
			$this->layout = false;
			$this->render('view-office', [
				'file' => $file_array[1],
				'filename' => $filename,
				'extension' => $extension,
				'streamUrl' => Yii::app()->createUrl('file/streamUrl', ['id' => $id]),
			]);
		} else {
			header('Content-Type: ' . $mimeType);
			header('Content-Disposition: attachment; filename="' . $filename . '"');
			echo substr($file_array[1], 1);
		}
		exit;
	}

	public function actionMultiDownload($fileIds)
	{
		$fileIds = explode(',', $fileIds);
		$zip = new ZipArchive;
		$zipname = "download-all.zip";
		if (file_exists($zipname)) unlink($zipname);
		$zip->open($zipname, ZipArchive::CREATE);
		foreach ($fileIds as $fileId) {
			$file = FileService::download($fileId);
			$file_array = explode("\n\r", $file, 2);
			$header_array = explode("\n", $file_array[0]);
			foreach ($header_array as $header_value) {
				$header_pieces = explode(':', $header_value);
				if (count($header_pieces) == 2) {
					$headers[$header_pieces[0]] = trim($header_pieces[1]);
				}
			}
			$filename = substr($headers['Content-Disposition'], (9 + strpos($headers['Content-Disposition'], "filename=")));

			$filename = trim($filename, '"');

			$zip->addFromString($filename, substr($file_array[1], 1));
		}
		$zip->close();
		header('Content-Type: application/zip');
		header('Content-disposition: attachment; filename=' . $zipname);
		header('Content-Length: ' . filesize($zipname));
		readfile($zipname);
		unlink($zipname);
	}
}
